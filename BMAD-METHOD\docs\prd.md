# **Ollama-to-OpenAI API Bridge Product Requirements Document (PRD)**

## **1. Goals and Background Context**

### **1.1. Goals**

- **Seamless Translation:** Successfully translate Ollama's /api/chat and /api/tags endpoint requests and responses to be fully compatible with an OpenAI-compatible backend service (/v1/chat/completions and /v1/models respectively). <sup>11</sup>

- **Streaming Parity:** Ensure real-time, token-by-token streaming from the backend service is correctly handled and passed through to the client, mimicking native Ollama streaming behavior.

- **Configuration Flexibility:** Enable the bridge to be easily configured with a target backend endpoint URL and the necessary API key for secure communication. <sup>2</sup>

- **Dynamic Model-Mapping:** Implement a robust configuration system that allows for mapping client-requested Ollama model names (e.g., llama3) to specific backend model IDs (e.g., gpt-4o-mini).


### **1.2. Background Context**

The proliferation of Large Language Models (LLMs) has created a diverse ecosystem, but a significant interoperability gap exists between locally-run open-source models via Ollama and proprietary cloud APIs like OpenAI. <sup>3</sup> Developers building applications for one platform often face substantial code modification when needing to switch to the other due to differing API formats. This project aims to close that gap.

This PRD outlines the requirements for a server-side translation bridge. It will act as a middleware that intercepts Ollama-formatted API calls, translates them into the OpenAI-compatible format for a backend service, and then translates the responses back for the client. <sup>4</sup> This will enable applications to switch between local and cloud LLMs without any client-side code changes, providing ultimate flexibility in development and deployment. <sup>5</sup>


## **2. Requirements**

### **2.1. Functional Requirements**

- **FR1:** The service must provide an endpoint GET /health for health checks.

- **FR2:** The service must translate requests from Ollama's POST /api/chat to a backend's POST /v1/chat/completions, including support for both streaming and non-streaming responses.

- **FR3:** The service must translate responses from a backend's GET /v1/models to Ollama's GET /api/tags format.

- **FR4:** The service must expose an endpoint GET /v1/models to list available models from the backend.

- **FR5:** The service must expose an endpoint GET /v1/TTI/models to list available Text-to-Image models.

- **FR6:** The service must expose an endpoint POST /v1/images/generations to handle image generation requests.

- **FR7:** The service must expose an endpoint POST /v1/auth/generate-key to generate API keys.

- **FR8:** The service must expose an endpoint GET /v1/auth/validate to validate an API key.

- **FR9:** The service must expose an endpoint GET /search to perform a web search.


### **2.2. Non-Functional Requirements**

- **NFR1:** The bridge must be configurable with the backend service's URL and a security token/API key.

- **NFR2:** The service must include a configurable mapping system to translate Ollama model names to backend-specific model IDs.

- **NFR3:** The latency overhead for non-streaming requests should be minimal, targeting an average of less than 100ms.

- **NFR4:** The service must not require authentication for incoming client requests, mirroring Ollama's default behavior. <sup>6</sup>

- **NFR5:** The application will be developed using Python and the FastAPI framework.


## **3. Technical Assumptions**

- **Primary Language:** Python (Version 3.11 or higher)

- **Primary Framework:** FastAPI

- **Testing requirements:** The testing strategy will include Unit Tests for translation logic, Integration Tests for endpoint functionality, and End-to-End Tests to validate key user flows against a test backend. <sup>7</sup>

- **Repository Structure:** A dedicated Polyrepo (a single repository for this service). <sup>8</sup>

- **Service Architecture:** A Monolithic Service architecture (a single, deployable application). <sup>9</sup>


## **4. Epics**

### **Epic 1: Foundational Bridge & Core Translation Service**

**Goal:** Establish the core FastAPI application, implement the primary Ollama-to-OpenAI translation for chat and model listing, and include all necessary configurations to create a testable, value-delivering product. <sup>10</sup>

- **Story 1.1: Project Setup & Health Check**

* As a developer, I want a basic FastAPI application structure with a /health endpoint, so that I have a verifiable foundation to build upon and can monitor the service's status. <sup>11</sup>

- **Story 1.2: Implement Configuration System**

* As a developer, I want to configure the bridge with a backend URL, API key, and model mappings from environment variables, so that I can easily deploy and manage the service without changing code.

- **Story 1.3: Non-Streaming Chat Translation**

* As a user, I want to send a non-streaming Ollama-formatted chat request to the bridge, so that it gets translated and forwarded to the OpenAI-compatible backend, and the response is translated back to me. <sup>12</sup>

- **Story 1.4: Streaming Chat Translation**

* As a user, I want to send a streaming Ollama-formatted chat request to the bridge, so that I receive the response token-by-token in real-time.

- **Story 1.5: Model List Translation**

* As a user, I want to request the model list from the bridge in the Ollama format, so that my client application can display the available models from the backend.

- **Story 1.6: Implement Model Name Mapping**

* As a user, I want the bridge to use a configured map to translate model names, so that when I request a generic model name, it is sent to the backend as the specific required model.


### **Epic 2: Expanded API Capabilities**

**Goal:** Build upon the foundational bridge by adding new, direct endpoints for model listing, image generation, and web search, transforming the service into a more comprehensive AI gateway. <sup>13</sup>

- **Story 2.1: Expose Backend Model List Directly**

* As a developer, I want to call a /v1/models endpoint on the bridge, so that I can get a direct, unfiltered list of models available from the backend service.

- **Story 2.2: List Text-to-Image Models**

* As a developer, I want a dedicated /v1/TTI/models endpoint, so that I can easily discover which models are for text-to-image generation.

- **Story 2.3 (Revised): Implement Image Generation Endpoint**

* As a user, I want to send a detailed request to POST /v1/images/generations, so that I can generate an image with specific parameters.

- **Story 2.4 (Revised): Implement Comprehensive Web Search Endpoint**

* As a user, I want to call a GET /search endpoint with detailed parameters, so that I can get tailored web search results.


### **Epic 3: Authentication Management Service**

**Goal:** To build a self-contained service within the bridge for generating and validating API keys, enabling a path to secure specific endpoints as needed.

- **Story 3.1: Design and Implement Secure Key Storage**

* As a developer, I want a secure mechanism to store API keys, so that keys are not exposed in plaintext.

- **Story 3.2: Implement Key Generation Endpoint**

* As a developer, I want to call the POST /v1/auth/generate-key endpoint, so that I can create a new, unique API key.

- **Story 3.3: Implement Key Validation Endpoint**

* As a developer, I want to call the GET /v1/auth/validate endpoint with a key, so that I can verify if it is valid.

---

## **5. Implementation Roadmap & Sequencing**

### **5.1. Development Phases**

**Phase 1: Foundation (Weeks 1-2)**
- Epic 1: Stories 1.1-1.2 (Project Setup & Configuration)
- **Deliverable:** Working FastAPI application with health check and configuration

**Phase 2: Core Translation (Weeks 3-4)**
- Epic 1: Stories 1.3-1.6 (Chat & Model Translation)
- **Deliverable:** Functional Ollama-to-OpenAI translation bridge

**Phase 3: Extended APIs (Weeks 5-6)**
- Epic 2: All stories (Image generation, search, direct endpoints)
- **Deliverable:** Complete API gateway functionality

**Phase 4: Authentication (Weeks 7-8)**
- Epic 3: All stories (Key management system)
- **Deliverable:** Secure API key management

### **5.2. Story Dependencies**

```mermaid
graph TD
    A[1.1: Project Setup] --> B[1.2: Configuration]
    B --> C[1.3: Non-Streaming Chat]
    B --> D[1.5: Model List]
    C --> E[1.4: Streaming Chat]
    C --> F[1.6: Model Mapping]
    D --> F
    F --> G[2.1: Direct Models]
    F --> H[2.2: TTI Models]
    G --> I[2.3: Image Generation]
    H --> I
    I --> J[2.4: Web Search]
    B --> K[3.1: Key Storage]
    K --> L[3.2: Key Generation]
    L --> M[3.3: Key Validation]
```

### **5.3. Priority Levels**

**P0 (Critical - MVP Blockers):**
- Story 1.1: Project Setup & Health Check
- Story 1.2: Configuration System
- Story 1.3: Non-Streaming Chat Translation

**P1 (High - Core Functionality):**
- Story 1.4: Streaming Chat Translation
- Story 1.5: Model List Translation
- Story 1.6: Model Name Mapping

**P2 (Medium - Extended Features):**
- Story 2.1: Direct Model List
- Story 2.2: TTI Models
- Story 2.3: Image Generation

**P3 (Low - Nice to Have):**
- Story 2.4: Web Search
- Epic 3: Authentication Management (all stories)

---

## **6. Detailed Story Breakdown**

### **Epic 1: Foundational Bridge & Core Translation Service**

#### **Story 1.1: Project Setup & Health Check** (Priority: P0, Effort: 1 day)

**User Story:**
As a developer, I want a basic FastAPI application structure with a /health endpoint, so that I have a verifiable foundation to build upon and can monitor the service's status.

**Acceptance Criteria:**
1. FastAPI application initializes successfully
2. GET /health endpoint returns 200 OK with status information
3. Application follows the project structure defined in architecture.md
4. Basic logging is configured and working
5. Application can be started locally with `uvicorn app.main:app --reload`

**Subtasks:**
- [ ] Initialize Poetry project with pyproject.toml
- [ ] Create project directory structure per architecture specification
- [ ] Set up FastAPI application in app/main.py
- [ ] Implement health check endpoint in app/api/routers/
- [ ] Configure basic logging
- [ ] Create .env.example file
- [ ] Write basic README with setup instructions
- [ ] Add .gitignore file

**Dependencies:** None

---

#### **Story 1.2: Implement Configuration System** (Priority: P0, Effort: 1 day)

**User Story:**
As a developer, I want to configure the bridge with a backend URL, API key, and model mappings from environment variables, so that I can easily deploy and manage the service without changing code.

**Acceptance Criteria:**
1. Configuration loads from environment variables
2. Required config: MEDUSA_BACKEND_URL, MODEL_MAPPINGS
3. Optional config: LOG_LEVEL, API_TIMEOUT
4. Configuration validation with clear error messages
5. Default values provided for optional settings
6. Configuration accessible via dependency injection

**Subtasks:**
- [ ] Create app/core/config.py with Pydantic Settings
- [ ] Define configuration schema with validation
- [ ] Implement model mapping configuration (JSON format)
- [ ] Add configuration dependency injection setup
- [ ] Create comprehensive .env.example
- [ ] Add configuration validation tests
- [ ] Document all configuration options

**Dependencies:** Story 1.1

---

#### **Story 1.3: Non-Streaming Chat Translation** (Priority: P0, Effort: 2 days)

**User Story:**
As a user, I want to send a non-streaming Ollama-formatted chat request to the bridge, so that it gets translated and forwarded to the OpenAI-compatible backend, and the response is translated back to me.

**Acceptance Criteria:**
1. POST /api/chat endpoint accepts Ollama chat format
2. Request is translated to OpenAI format for backend
3. Backend response is translated back to Ollama format
4. Non-streaming responses work correctly
5. Error handling for backend failures
6. Request/response logging for debugging

**Subtasks:**
- [ ] Create Ollama chat request/response Pydantic schemas
- [ ] Create OpenAI chat request/response Pydantic schemas
- [ ] Implement translation service with strategy pattern
- [ ] Create backend client service with HTTPX
- [ ] Implement POST /api/chat router endpoint
- [ ] Add comprehensive error handling
- [ ] Write unit tests for translation logic
- [ ] Write integration tests for endpoint

**Dependencies:** Story 1.2

---

#### **Story 1.4: Streaming Chat Translation** (Priority: P1, Effort: 2 days)

**User Story:**
As a user, I want to send a streaming Ollama-formatted chat request to the bridge, so that I receive the response token-by-token in real-time.

**Acceptance Criteria:**
1. POST /api/chat with stream=true parameter works
2. Streaming responses are properly formatted for Ollama
3. Each chunk is translated from OpenAI to Ollama format
4. Connection handling for interrupted streams
5. Proper cleanup of streaming connections
6. Performance meets <100ms first token requirement

**Subtasks:**
- [ ] Implement streaming response handling in backend client
- [ ] Create streaming translation logic
- [ ] Add streaming support to /api/chat endpoint
- [ ] Implement proper connection management
- [ ] Add streaming error handling
- [ ] Write streaming integration tests
- [ ] Performance testing for streaming latency

**Dependencies:** Story 1.3

---

#### **Story 1.5: Model List Translation** (Priority: P1, Effort: 1 day)

**User Story:**
As a user, I want to request the model list from the bridge in the Ollama format, so that my client application can display the available models from the backend.

**Acceptance Criteria:**
1. GET /api/tags endpoint returns Ollama-formatted model list
2. Backend /v1/models response is translated correctly
3. Model metadata is preserved where possible
4. Error handling for backend model list failures
5. Caching of model list for performance (optional)

**Subtasks:**
- [ ] Create model list Pydantic schemas (Ollama & OpenAI)
- [ ] Implement model list translation logic
- [ ] Create GET /api/tags router endpoint
- [ ] Add backend model list client method
- [ ] Implement error handling
- [ ] Write unit and integration tests

**Dependencies:** Story 1.2

---

#### **Story 1.6: Implement Model Name Mapping** (Priority: P1, Effort: 1 day)

**User Story:**
As a user, I want the bridge to use a configured map to translate model names, so that when I request a generic model name, it is sent to the backend as the specific required model.

**Acceptance Criteria:**
1. Model name mapping works in chat requests
2. Model name mapping works in model list responses
3. Configuration supports flexible mapping rules
4. Fallback behavior for unmapped models
5. Clear error messages for invalid model names

**Subtasks:**
- [ ] Extend configuration to support model mappings
- [ ] Implement model name translation logic
- [ ] Integrate mapping into chat translation
- [ ] Integrate mapping into model list translation
- [ ] Add validation for model mapping configuration
- [ ] Write comprehensive tests for mapping logic

**Dependencies:** Stories 1.3, 1.5

---

### **Epic 2: Expanded API Capabilities**

#### **Story 2.1: Expose Backend Model List Directly** (Priority: P2, Effort: 0.5 days)

**User Story:**
As a developer, I want to call a /v1/models endpoint on the bridge, so that I can get a direct, unfiltered list of models available from the backend service.

**Acceptance Criteria:**
1. GET /v1/models endpoint returns OpenAI-formatted model list
2. Response is direct passthrough from backend
3. No translation or filtering applied
4. Proper error handling for backend failures

**Subtasks:**
- [ ] Create GET /v1/models router endpoint
- [ ] Implement direct passthrough to backend
- [ ] Add error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2

---

#### **Story 2.2: List Text-to-Image Models** (Priority: P2, Effort: 0.5 days)

**User Story:**
As a developer, I want a dedicated /v1/TTI/models endpoint, so that I can easily discover which models are for text-to-image generation.

**Acceptance Criteria:**
1. GET /v1/TTI/models endpoint returns filtered model list
2. Only text-to-image models are included
3. Model filtering logic is configurable
4. Proper error handling and fallbacks

**Subtasks:**
- [ ] Implement model filtering logic
- [ ] Create GET /v1/TTI/models router endpoint
- [ ] Add configuration for TTI model identification
- [ ] Write unit and integration tests

**Dependencies:** Story 2.1

---

#### **Story 2.3: Implement Image Generation Endpoint** (Priority: P2, Effort: 1 day)

**User Story:**
As a user, I want to send a detailed request to POST /v1/images/generations, so that I can generate an image with specific parameters.

**Acceptance Criteria:**
1. POST /v1/images/generations accepts OpenAI-compatible requests
2. Request is forwarded to backend image generation API
3. Response includes generated image data
4. Proper error handling for generation failures
5. Support for various image parameters (size, quality, etc.)

**Subtasks:**
- [ ] Create image generation request/response schemas
- [ ] Implement POST /v1/images/generations endpoint
- [ ] Add backend image generation client method
- [ ] Implement parameter validation
- [ ] Add comprehensive error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2

---

#### **Story 2.4: Implement Web Search Endpoint** (Priority: P3, Effort: 1 day)

**User Story:**
As a user, I want to call a GET /search endpoint with detailed parameters, so that I can get tailored web search results.

**Acceptance Criteria:**
1. GET /search endpoint accepts query parameters
2. Request is forwarded to backend search API
3. Search results are returned in appropriate format
4. Support for search engine selection
5. Proper error handling for search failures

**Subtasks:**
- [ ] Create search request/response schemas
- [ ] Implement GET /search endpoint with query parameters
- [ ] Add backend search client method
- [ ] Implement parameter validation
- [ ] Add error handling
- [ ] Write integration tests

**Dependencies:** Story 1.2

---

### **Epic 3: Authentication Management Service**

#### **Story 3.1: Design and Implement Secure Key Storage** (Priority: P3, Effort: 1 day)

**User Story:**
As a developer, I want a secure mechanism to store API keys, so that keys are not exposed in plaintext.

**Acceptance Criteria:**
1. API keys are hashed before storage
2. SQLite database is used for key storage
3. Database schema includes key metadata
4. Secure hashing algorithm (bcrypt/argon2)
5. Database initialization and migration support

**Subtasks:**
- [ ] Design database schema for API keys
- [ ] Implement database connection and models
- [ ] Create secure hashing utilities
- [ ] Implement database initialization
- [ ] Add database migration support
- [ ] Write unit tests for storage layer

**Dependencies:** Story 1.2

---

#### **Story 3.2: Implement Key Generation Endpoint** (Priority: P3, Effort: 0.5 days)

**User Story:**
As a developer, I want to call the POST /v1/auth/generate-key endpoint, so that I can create a new, unique API key.

**Acceptance Criteria:**
1. POST /v1/auth/generate-key generates secure API keys
2. Keys are cryptographically random and unique
3. Generated key is returned in response
4. Key metadata is stored in database
5. Proper error handling for generation failures

**Subtasks:**
- [ ] Implement secure key generation logic
- [ ] Create POST /v1/auth/generate-key endpoint
- [ ] Add key storage integration
- [ ] Implement response formatting
- [ ] Add error handling
- [ ] Write unit and integration tests

**Dependencies:** Story 3.1

---

#### **Story 3.3: Implement Key Validation Endpoint** (Priority: P3, Effort: 0.5 days)

**User Story:**
As a developer, I want to call the GET /v1/auth/validate endpoint with a key, so that I can verify if it is valid.

**Acceptance Criteria:**
1. GET /v1/auth/validate accepts API key parameter
2. Key validation against stored hashes
3. Returns validation status and key metadata
4. Proper error handling for invalid keys
5. Optional key expiration support

**Subtasks:**
- [ ] Implement key validation logic
- [ ] Create GET /v1/auth/validate endpoint
- [ ] Add database lookup integration
- [ ] Implement response formatting
- [ ] Add error handling
- [ ] Write unit and integration tests

**Dependencies:** Story 3.2

---

## **7. Timeline Estimates & Milestones**

### **Development Timeline (8 weeks total)**

**Week 1: Foundation Setup**
- Story 1.1: Project Setup & Health Check (1 day)
- Story 1.2: Configuration System (1 day)
- Environment setup and tooling (3 days)

**Week 2: Core Translation - Part 1**
- Story 1.3: Non-Streaming Chat Translation (2 days)
- Story 1.5: Model List Translation (1 day)
- Testing and refinement (2 days)

**Week 3: Core Translation - Part 2**
- Story 1.4: Streaming Chat Translation (2 days)
- Story 1.6: Model Name Mapping (1 day)
- Integration testing (2 days)

**Week 4: Epic 1 Completion**
- Bug fixes and performance optimization (2 days)
- Documentation and deployment prep (2 days)
- **Milestone 1: Core Bridge Functional**

**Week 5: Extended APIs - Part 1**
- Story 2.1: Direct Model List (0.5 days)
- Story 2.2: TTI Models (0.5 days)
- Story 2.3: Image Generation (1 day)
- Testing and integration (3 days)

**Week 6: Extended APIs - Part 2**
- Story 2.4: Web Search (1 day)
- Epic 2 testing and refinement (2 days)
- **Milestone 2: Complete API Gateway**

**Week 7: Authentication System**
- Story 3.1: Secure Key Storage (1 day)
- Story 3.2: Key Generation (0.5 days)
- Story 3.3: Key Validation (0.5 days)
- Testing and integration (3 days)

**Week 8: Final Integration & Deployment**
- End-to-end testing (2 days)
- Performance testing and optimization (1 day)
- Production deployment setup (2 days)
- **Milestone 3: Production Ready**

### **Risk Mitigation Timeline**
- **Buffer Time:** 20% added to each estimate
- **Parallel Development:** Some stories can be developed in parallel
- **Early Testing:** Continuous integration from Week 1

---

## **8. Success Metrics & Validation Criteria**

### **MVP Success Criteria**
1. **Functional Completeness:** All P0 and P1 stories completed
2. **Performance:** <100ms latency for non-streaming requests
3. **Compatibility:** 100% Ollama API format compliance
4. **Reliability:** 99% uptime during testing period
5. **Testing:** 80%+ code coverage with passing tests

### **Validation Approach**
1. **Unit Testing:** Each component tested in isolation
2. **Integration Testing:** API endpoints tested end-to-end
3. **Performance Testing:** Load testing with realistic scenarios
4. **Compatibility Testing:** Validation against real Ollama clients
5. **User Acceptance Testing:** Developer feedback on ease of use

### **Go-Live Criteria**
- [ ] All P0 and P1 stories completed and tested
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Documentation complete
- [ ] Deployment pipeline functional
- [ ] Monitoring and alerting configured