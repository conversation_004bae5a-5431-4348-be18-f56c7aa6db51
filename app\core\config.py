"""
Application configuration management.

This module handles loading and validation of configuration from environment variables
using Pydantic Settings for type safety and validation.
"""

import json
from functools import lru_cache
from typing import Dict, Any

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    
    All configuration is loaded from environment variables with validation
    and default values where appropriate.
    """
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )

    # Backend API Configuration
    medusa_backend_url: str = Field(
        ...,
        description="Base URL for the MedusaXD backend API"
    )

    api_timeout: int = Field(
        30,
        description="Timeout in seconds for backend API requests",
        ge=1,
        le=300
    )

    # Model Mapping Configuration
    model_mappings: Dict[str, str] = Field(
        default_factory=dict,
        description="JSON mapping of Ollama model names to backend model IDs"
    )

    # Logging Configuration
    log_level: str = Field(
        "INFO",
        description="Logging level"
    )

    # Application Configuration
    app_name: str = Field(
        "Ollama-to-OpenAI Bridge",
        description="Application name"
    )

    debug: bool = Field(
        False,
        description="Enable debug mode"
    )

    # Performance Configuration
    max_concurrent_requests: int = Field(
        100,
        description="Maximum number of concurrent requests to backend",
        ge=1,
        le=1000
    )
    
    @field_validator("model_mappings", mode="before")
    @classmethod
    def parse_model_mappings(cls, v: Any) -> Dict[str, str]:
        """
        Parse model mappings from JSON string or return dict as-is.
        
        Args:
            v: Raw model mappings value
            
        Returns:
            Dict[str, str]: Parsed model mappings
            
        Raises:
            ValueError: If JSON parsing fails
        """
        if isinstance(v, str):
            try:
                parsed = json.loads(v)
                if not isinstance(parsed, dict):
                    raise ValueError("Model mappings must be a JSON object")
                return {str(k): str(v) for k, v in parsed.items()}
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in model mappings: {e}")
        elif isinstance(v, dict):
            return {str(k): str(v) for k, v in v.items()}
        elif v is None or v == "":
            return {}
        else:
            raise ValueError("Model mappings must be a JSON string or dict")
    
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """
        Validate log level is one of the standard logging levels.
        
        Args:
            v: Log level string
            
        Returns:
            str: Validated log level
            
        Raises:
            ValueError: If log level is invalid
        """
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        v_upper = v.upper()
        if v_upper not in valid_levels:
            raise ValueError(f"Log level must be one of: {', '.join(valid_levels)}")
        return v_upper


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    
    Uses LRU cache to ensure settings are loaded only once and reused
    throughout the application lifecycle.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()
