# **Ollama-to-OpenAI API Bridge Architecture Document**

## **1. Introduction / Preamble**

### This document outlines the overall project architecture for the **Ollama-to-OpenAI API Bridge**. <sup>14</sup> Its primary goal is to serve as the guiding architectural blueprint for development, ensuring consistency and adherence to chosen patterns and technologies. <sup>15</sup> The bridge will function as a middleware service, translating requests from an Ollama-compatible format to the format required by the **MedusaXD Public API** and translating the responses back.

## **2. Table of Contents**

### _(This will be populated as we complete the document.)_

## **3. Technical Summary**

### The system is a monolithic, server-side application built with **Python** and **FastAPI**. It acts as a translation proxy, receiving requests formatted for a local Ollama instance and transforming them for a backend OpenAI-compatible service (MedusaXD API). The architecture is designed to be lightweight, stateless, and easily configurable, with a primary focus on low-latency request and response handling for both streaming and non-streaming chat completions.

## **4. High-Level Overview**

### The architecture follows a simple **Proxy/Facade** pattern. <sup>16</sup> All client traffic is directed to this bridge application. The bridge is responsible for all logic required to adapt the incoming requests to the specification of the backend MedusaXD API. <sup>17</sup> This design decouples the client application from the specific implementation details of the backend service.The primary data flow is as follows:Code snippet sequenceDiagram     participant Client     participant Ollama-Bridge as Ollama-to-OpenAI Bridge     participant MedusaXD\_API as MedusaXD Public API     Client->>+Ollama-Bridge: Request (Ollama Format)     Ollama-Bridge->>Ollama-Bridge: Translate Request     Ollama-Bridge->>+MedusaXD\_API: API Call (OpenAI Format)     MedusaXD\_API-->>-Ollama-Bridge: API Response (OpenAI Format)     Ollama-Bridge->>Ollama-Bridge: Translate Response     Ollama-Bridge-->>-Client: Response (Ollama Format)

## **5. Architectural / Design Patterns Adopted**

### To ensure the application is maintainable, testable, and extensible, we will adhere to the following key design patterns:* **Adapter Pattern:** This is the core pattern for the service. A dedicated Translation layer will be created to adapt incoming requests from the Ollama API format to the MedusaXD (OpenAI-compatible) API format, and vice-versa for responses.

* **Proxy Pattern:** The bridge as a whole acts as a proxy, controlling and managing access to the backend MedusaXD API from client applications.

* **Facade Pattern:** The bridge provides a simplified, unified Ollama-style interface, hiding the complexities of the various backend services (chat, image generation, search) that are being called.

* **Strategy Pattern:** Within the translation layer, each specific translation type (e.g., chat completion, model list) will be implemented as a separate "strategy" class. This allows new translation types to be added cleanly without modifying existing logic.

* **Dependency Injection:** We will leverage FastAPI's native support for dependency injection to provide services, configuration, and clients throughout the application. This decouples components and makes them easier to test.

## **6. Component View**

### The application will be logically divided into several key components, each with a distinct responsibility. <sup>18</sup> This separation of concerns is crucial for modularity and maintainability.* **API Layer (Routers):** This is the entry point for all incoming HTTP requests. It is responsible for path routing, request validation using Pydantic models, and passing requests to the appropriate service layer. We will have separate routers for different parts of the API (e.g., /api/, /v1/, /v1/auth/).

* **Translation Service:** This core component contains the business logic for the adapter pattern. It will select the appropriate translation strategy (e.g., ChatTranslationStrategy) and orchestrate the transformation of both requests and responses.

* **Backend Client Service:** This component is solely responsible for communicating with the external MedusaXD Public API. It will encapsulate an httpx client, manage connection details, and handle the mechanics of sending requests and receiving responses.

* **Configuration Module:** A dedicated module responsible for loading all configuration from environment variables (as defined in the PRD) and making it available via dependency injection.

* **Data Models (Pydantic):** A set of Pydantic models that strictly define the schemas for all API requests and responses (both for the incoming Ollama format and the outgoing MedusaXD format). This ensures robust data validation at the boundaries of the system.The interaction between these components can be visualized as follows:Code snippet graph TD     subgraph "Ollama-to-OpenAI Bridge"         A\[API Layer / Routers] --> B(Translation Service);         B --> C{Backend Client Service};         D(Configuration Module) --> C;         D --> B;         subgraph "Data Models (Pydantic)"             E\[Ollama Models]             F\[MedusaXD Models]         end         A -- Uses --> E;         B -- Uses --> E;         B -- Uses --> F;         C -- Uses --> F;     end     Client -- HTTP Request --> A;     C -- HTTP Request --> G\[MedusaXD Public API];

## **7. Project Structure**

### This section outlines the definitive directory structure. <sup>19</sup> All new code must be placed in the appropriate location as described below. Plaintext ollama\_bridge/ ├── .github/ │   └── workflows/ │       └── ci.yml            # Continuous Integration workflow ├── app/                      # Main application source code │   ├── \_\_init\_\_.py │   ├── api/                  # API layer: routers and schemas │   │   ├── \_\_init\_\_.py │   │   ├── routers/ │   │   │   ├── \_\_init\_\_.py │   │   │   ├── ollama\_router.py │   │   │   ├── v1\_router.py │   │   │   └── auth\_router.py │   │   └── schemas/ │   │       ├── \_\_init\_\_.py │   │       ├── ollama\_schemas.py │   │       └── medusa\_schemas.py │   ├── core/                 # Core logic, config, and clients │   │   ├── \_\_init\_\_.py │   │   └── config.py         # Pydantic settings management │   ├── services/             # Business logic and external service clients │   │   ├── \_\_init\_\_.py │   │   ├── translation\_service.py │   │   └── backend\_client\_service.py │   └── main.py               # FastAPI application entry point ├── docs/                     # Project documentation (PRD, Arch, etc.) ├── tests/                    # Automated tests │   ├── \_\_init\_\_.py │   ├── test\_api/ │   └── test\_services/ ├── .env.example              # Example environment variables ├── .gitignore ├── Dockerfile                # Containerization instructions ├── pyproject.toml            # Project dependencies and metadata (Poetry/Pip) └── README.md                 # Project overview and setup instructions

### **Key Directory Descriptions**

### * **app/**: Contains all the application's source code.

* **app/api/**: This package holds everything related to the HTTP interface.* **app/api/routers/**: Contains the FastAPI routers, with each file corresponding to a logical group of endpoints (e.g., ollama\_router.py for /api/\* endpoints).

* **app/api/schemas/**: Contains all Pydantic models used for request/response validation and serialization.- **app/core/**: Holds core application logic, with config.py being responsible for loading and providing all configuration settings. <sup>20</sup>

- **app/services/**: Contains the business logic. translation\_service.py will hold the different translation strategies, and backend\_client\_service.py will manage communication with the MedusaXD API.

- **app/main.py**: The main entry point that initializes the FastAPI app, includes the routers, and runs the Uvicorn server.

- **tests/**: Contains all automated tests, mirroring the app/ structure. <sup>21</sup>

## **8. API Reference**

### **External APIs Consumed**

### This section details the external MedusaXD Public API, which provides all backend functionalities for the bridge.

#### **MedusaXD Public API**

### * **Purpose:** A unified, public API providing AI chat, image generation, and web search functionalities.

* **Base URL:** The base URL will be provided via the {MEDUSA\_BACKEND\_URL} environment variable.

* **Authentication:** No API key is required, as stated in the API documentation.

* **Rate Limits:** Not specified in the provided API documentation.

* **Link to Official Docs:** The OpenAPI spec is available at the /openapi.json path of the service.

* **Key Endpoints Used:*** **POST /v1/chat/completions*** **Description:** Handles chat completion requests, supporting both streaming and non-streaming responses.

* **Request Body Schema:** Conforms to the ChatCompletionRequest schema defined in the MedusaXD documentation. This will be implemented as a Pydantic model in app/api/schemas/medusa\_schemas.py.

* **Success Response Schema:** A JSON object containing the chat completion. The exact structure will be modeled in our Pydantic schemas.* **POST /v1/images/generations*** **Description:** Handles requests to generate images from a text prompt.

* **Request Body Schema:** Conforms to the ImageGenerationRequest schema defined in the MedusaXD documentation. This will be implemented as a Pydantic model.

* **Success Response Schema:** A JSON object containing the generated image data (e.g., URLs or base64 strings).* **GET /v1/models*** **Description:** Lists all available models from the backend service.

* **Request Body Schema:** None.

* **Success Response Schema:** Conforms to the ModelListResponse schema, containing a list of ModelInfo objects.* **GET /search**- **Description:** Performs a web search using the backend service.

- **Request Body Schema:** None. Request parameters are sent via URL query string (e.g., ?q=...\&engine=...).

- **Success Response Schema:** A JSON object containing the search results.

## **9. Data Models**

### **API Payload Schemas**

### These Pydantic models will define the data structures for all API interactions, ensuring robust validation at the application's boundaries.

#### **Ollama-Format Schemas**

### _(These models represent the public-facing API of our bridge, mimicking the standard Ollama API format.)_- **OllamaChatRequest**- **Description:** Represents an incoming chat request from a client.

- **Schema Definition:** TypeScript interface OllamaChatRequest {   model: string;   messages: {     role: 'system' | 'user' | 'assistant';     content: string;     images?: string\[]; // base64 encoded images   }\[];   stream?: boolean;   // other optional parameters like 'options', 'format', etc. }- **OllamaChatResponse**- **Description:** Represents a standard, non-streaming response sent back to the client.

- **Schema Definition:** TypeScript interface OllamaChatResponse {   model: string;   created\_at: string; // ISO 8601 datetime   message: {     role: 'assistant';     content: string;   };   done: true;   // other metadata fields like 'total\_duration', 'eval\_count', etc. }- **OllamaTagsResponse*** **Description:** Represents the response for a model list request.

* **Schema Definition:** TypeScript interface OllamaTagsResponse {   models: {     name: string; // e.g., "llama3:latest"     modified\_at: string; // ISO 8601 datetime     size: number; // in bytes   }\[]; }

#### **MedusaXD-Format Schemas**

### _(These models represent the data sent to and received from the backend MedusaXD API, based on the provided OpenAPI specification.)_- **MedusaChatRequest**- **Description:** Represents the chat request sent to the MedusaXD backend. Conforms to the ChatCompletionRequest schema.

- **Schema Definition:** TypeScript interface MedusaChatRequest {   model: string;   messages: {     role: 'system' | 'user' | 'assistant' | 'tool' | 'function';     content: string | object\[] | null; // Can be string or multimodal parts   }\[];   stream?: boolean;   temperature?: number;   max\_tokens?: number; }- **MedusaModelListResponse*** **Description:** Represents the model list response received from the MedusaXD backend. Conforms to the ModelListResponse schema.

* **Schema Definition:** TypeScript interface MedusaModelListResponse {   object: 'list';   data: {     id: string;     object: 'model';     created: number; // Unix timestamp     owned\_by: string;   }\[]; }

### **Database Schemas**

### _(This schema is required to support Epic 3: Authentication Management Service)_- **api\_keys Table*** **Purpose:** To securely store hashed API keys for validation.

* **Schema Definition:** SQL CREATE TABLE api\_keys (   id SERIAL PRIMARY KEY,   hashed\_key VARCHAR(255) NOT NULL UNIQUE,   username VARCHAR(50) NOT NULL,   created\_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT\_TIMESTAMP,   expires\_at TIMESTAMP WITH TIME ZONE );

## **10. Core Workflow / Sequence Diagrams**

### **1. Non-Streaming Chat Completion Workflow**

### This diagram illustrates the "happy path" for a standard, non-streaming chat request, showing how it flows through the internal components of the bridge. <sup>22</sup> Code snippet sequenceDiagram     participant Client     participant API\_Layer as API Layer (Routers)     participant Translator as Translation Service     participant Backend\_Client as Backend Client Service     participant MedusaXD\_API as MedusaXD API     Client->>+API\_Layer: POST /api/chat (OllamaChatRequest)     API\_Layer->>+Translator: process\_chat\_request(request\_data)     Translator->>Translator: Adapt Ollama request to MedusaXD format     Translator->>+Backend\_Client: send\_chat\_request(MedusaChatRequest)     Backend\_Client->>+MedusaXD\_API: POST /v1/chat/completions     MedusaXD\_API-->>-Backend\_Client: MedusaChatResponse     Backend\_Client-->>-Translator: response     Translator->>Translator: Adapt MedusaXD response to Ollama format     Translator-->>-API\_Layer: OllamaChatResponse     API\_Layer-->>-Client: 200 OK (OllamaChatResponse)

### **2. Streaming Chat Completion Workflow**

### This diagram shows how a streaming request is handled, with the bridge translating and forwarding each data chunk as it arrives.Code snippet sequenceDiagram     participant Client     participant API\_Layer as API Layer (Routers)     participant Translator as Translation Service     participant Backend\_Client as Backend Client Service     participant MedusaXD\_API as MedusaXD API     Client->>+API\_Layer: POST /api/chat (stream=true)     API\_Layer->>+Translator: process\_streaming\_chat(request\_data)     Translator->>+Backend\_Client: send\_streaming\_chat\_request(MedusaChatRequest)     Backend\_Client->>+MedusaXD\_API: POST /v1/chat/completions (stream=true)     MedusaXD\_API-->>-Backend\_Client: Stream of data chunks     loop For each chunk         Backend\_Client-->>Translator: chunk         Translator->>Translator: Adapt chunk to Ollama format         Translator-->>API\_Layer: translated\_chunk         API\_Layer-->>Client: Send chunk     end

### **3. API Key Generation & Validation Workflow**

### This diagram illustrates the two main authentication flows defined in Epic 3.Code snippet sequenceDiagram     box "Key Generation"         participant Client\_Admin as Admin Client         participant Auth\_Router as Auth Router         participant DB as Database         Client\_Admin->>+Auth\_Router: POST /v1/auth/generate-key         Auth\_Router->>Auth\_Router: Generate new key & hash         Auth\_Router->>+DB: Store hashed\_key         DB-->>-Auth\_Router: Success         Auth\_Router-->>-Client\_Admin: 200 OK (plaintext\_key)     end     box "Key Validation"         participant App\_Client as Application Client         participant V1\_Router as V1 Router (Protected)         App\_Client->>+V1\_Router: GET /v1/images/generations (Authorization: Bearer \<key>)         V1\_Router->>+Auth\_Router: validate\_key(key)         Auth\_Router->>+DB: Query for hashed\_key         DB-->>-Auth\_Router: Found / Not Found         Auth\_Router-->>-V1\_Router: Validation Result (Valid/Invalid)         alt Key is Valid             V1\_Router->>V1\_Router: Process request...         else Key is Invalid             V1\_Router-->>-App\_Client: 401 Unauthorized         end     end

## **11. Definitive Tech Stack Selections**

### This list is the definitive standard for the project. <sup>23</sup>|                     |                |                       |                                                                                          |                                                                                                                       |
| ------------------- | -------------- | --------------------- | ---------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------- |
| **Category**        | **Technology** | **Version / Details** | **Description / Purpose**                                                                | **Justification (Optional)**                                                                                          |
| **Languages**       | Python         | 3.11                  | Primary language for the application backend.                                            | Modern, well-supported, and excellent for API development with a rich ecosystem.                                      |
| **Frameworks**      | FastAPI        | Latest                | High-performance ASGI framework for building APIs.                                       | Provides automatic data validation, dependency injection, and interactive API docs.                                   |
| **Web Server**      | Uvicorn        | Latest                | Lightning-fast ASGI server, required to run FastAPI.                                     | The standard and recommended server for FastAPI applications.                                                         |
| **Databases**       | SQLite         | 3.x                   | Lightweight, file-based SQL database for storing hashed API keys.                        | Simple, no external setup required, and perfectly sufficient for the limited persistence needs of Epic 3.             |
| **Testing**         | Pytest         | Latest                | Powerful and flexible testing framework for Python.                                      | A mature and feature-rich framework that is the de-facto standard for testing in the Python community.                |
| **HTTP Client**     | HTTPX          | Latest                | A fully featured asynchronous HTTP client for Python.                                    | Required for making non-blocking calls to the backend MedusaXD API, which is essential for a high-performance bridge. |
| **Infrastructure**  | Docker         | Latest                | Platform for developing, shipping, and running applications in containers. <sup>24</sup> | Ensures consistency between development and production environments and simplifies deployment.                        |
| **CI/CD**           | GitHub Actions | N/A                   | Automation platform for build, test, and deployment pipelines. <sup>25</sup>             | Tightly integrated with GitHub for seamless continuous integration and deployment workflows.                          |
| **Dep. Management** | Poetry         | Latest                | Tool for Python dependency management and packaging.                                     | Provides deterministic dependency resolution and a superior workflow compared to traditional requirements.txt files.  |

## **12. Infrastructure and Deployment Overview**

### * **Cloud Provider(s):** The architecture is cloud-agnostic. However, the initial deployment will target a major cloud provider (e.g., AWS, GCP, Azure) that offers a simple, serverless container platform.

* **Core Services Used:** We will use a **Serverless Container Platform** (e.g., AWS App Runner or Google Cloud Run). This choice abstracts away the complexity of managing servers and clusters, aligning with the project's lightweight nature.

* **Infrastructure as Code (IaC):** We will use **Terraform** to define and manage all necessary cloud resources (like the container service itself). All Terraform code will reside in the infra/ directory.

* **Deployment Strategy:** A **CI/CD pipeline** will be implemented using GitHub Actions. <sup>26</sup> Every push to the main branch will automatically trigger the pipeline to build, test, and deploy the new version of the application.

* **Environments:** We will start with two environments: <sup>27</sup>* **Development:** The local machine environment where developers run the service.

* **Production:** The live environment hosted on the cloud provider.- **Environment Promotion:** A push to the main branch, after all automated tests have passed, will trigger a direct deployment to the Production environment. <sup>28</sup>

- **Rollback Strategy:** We will rely on the managed container service's built-in rollback capabilities. A rollback can be triggered manually via the cloud provider's console or an API call to redeploy the previous stable container image. <sup>29</sup>

## **13. Error Handling Strategy**

### * **General Approach:** The application will use Python's standard exception system. <sup>30</sup> We will define a hierarchy of custom exception classes (e.g., TranslationError, BackendAPIError) to handle specific, known error states with more context than generic exceptions.

* **Logging:*** **Library/Method:** We will use Python's built-in logging module, which is the industry standard. <sup>31</sup>

* **Format:** All logs will be emitted in **JSON format**. <sup>32</sup> This structured logging approach allows for easier parsing, searching, and analysis by modern observability platforms.

* **Levels:** Standard logging levels will be used: <sup>33</sup>* DEBUG: Detailed information, typically of interest only when diagnosing problems.

* INFO: Confirmation that things are working as expected.

* WARNING: An indication that something unexpected happened, or a sign of some problem in the near future (e.g., 'disk space low'). The software is still working as expected.

* ERROR: Due to a more serious problem, the software has not been able to perform some function.

* CRITICAL: A serious error, indicating that the program itself may be unable to continue running.* **Context:** To facilitate request tracing, a unique **Correlation ID** will be generated for each incoming request and included in all related log messages. <sup>34</sup>* **Specific Handling Patterns:*** **External API Calls:*** All calls made with the HTTPX client to the MedusaXD API will have explicit timeouts.

* We will use a library like tenacity to implement a retry mechanism with exponential backoff for transient errors (e.g., 5xx server errors, network issues). <sup>35</sup>

* Client errors (4xx) from the backend will be logged and generally result in a 502 Bad Gateway response from our bridge to our client.- **Internal Errors / Business Logic Exceptions:** We will use FastAPI's custom exception handlers. <sup>36</sup> When a custom exception (e.g., TranslationError) is raised, a specific handler will catch it and return a clear, user-friendly HTTP error response, preventing internal stack traces from being leaked to the client.

## **14. Coding Standards**

### * **Style Guide & Linter:** We will use **Ruff** as the primary tool for both linting and code formatting. <sup>37</sup> A ruff.toml or pyproject.toml \[tool.ruff] configuration will be committed to the repository to enforce a consistent style.

* **Naming Conventions:** All code must follow standard Python [PEP 8](https://peps.python.org/pep-0008/) naming conventions: <sup>38</sup>* snake\_case for variables, functions, methods, and filenames.

* PascalCase for classes.

* UPPER\_SNAKE\_CASE for constants.- **File Structure:** Code must strictly adhere to the layout defined in the "Project Structure" section of this document. <sup>39</sup>

- **Unit Test File Organization:** Unit tests will be placed in the tests/ directory, mirroring the application's package structure. <sup>40</sup> Test files must be named using the test\_\*.py convention.

- **Asynchronous Operations:** All I/O-bound operations (e.g., HTTP requests to the backend, database calls) **must** use the async and await keywords. <sup>41</sup>

- **Type Safety:** All new functions and methods **must** include full type hints. <sup>42</sup> We will use **Mypy** in our CI/CD pipeline to statically check types and prevent type-related errors. <sup>43</sup>

- **Comments & Documentation:** Code should be self-documenting where possible. For complex logic, use **Google-style Python docstrings** to explain the "why," not the "what." <sup>44</sup>

- **Dependency Management:** All project dependencies must be managed through **Poetry** and defined in the pyproject.toml file. <sup>45</sup> The poetry.lock file will be committed to ensure deterministic builds.

### **Detailed FastAPI Conventions**

### - **Dependency Injection:** Services, configuration objects, and database sessions must be made available to the API layer using FastAPI's dependency injection system (Depends). <sup>46</sup>

- **Data Validation:** All API request and response bodies must be defined using **Pydantic** models within the app/api/schemas/ directory to leverage FastAPI's automatic data validation and serialization. <sup>47</sup>

- **Resource Management:** For any resources that require explicit cleanup (like file handles or database connections, though our stack may abstract this), the with statement must be used. <sup>48</sup>

## **15. Overall Testing Strategy**

### This strategy outlines the different levels of testing that will be implemented for the project. <sup>49</sup>- **Tools:** The primary testing framework will be **Pytest**. <sup>50</sup> For testing API endpoints and mocking HTTP requests, we will use FastAPI's TestClient and httpx's built-in mocking capabilities.

- **Unit Tests:**- **Scope:** Test individual functions and classes in complete isolation. <sup>51</sup> This is particularly important for all translation logic within the TranslationService.

- **Location:** Reside in the tests/ directory, mirroring the application structure. <sup>52</sup>

- **Mocking/Stubbing:** All external dependencies, such as the BackendClientService, will be mocked out so that unit tests never make real network calls. <sup>53</sup>

- **AI Agent Responsibility:** The developer agent is required to generate comprehensive unit tests covering all significant logic paths, edge cases, and error conditions for any new or modified code. <sup>54</sup>- **Integration Tests:**- **Scope:** Test the complete request-to-response cycle for each API endpoint, validating the interaction between the API layer, services, and schemas. <sup>55</sup> The actual call to the external MedusaXD API will be mocked at the BackendClientService level.

- **Environment:** These tests will use FastAPI's TestClient to send requests to the application in memory, without needing to run a live server.

- **AI Agent Responsibility:** The developer agent must create integration tests for every API endpoint to verify its behavior, including input validation and error handling.- **End-to-End (E2E) Tests:**- **Scope:** Validate the entire application flow by running the containerized bridge and having it connect to a live instance of the MedusaXD API. These tests will verify the translations against the real backend.

- **Tools:** E2E tests will be implemented as Python scripts using the httpx library to act as the client. <sup>56</sup>- **Test Coverage:**- **Target:** We will aim for a minimum of **80% line coverage** for unit tests, but the primary focus will be on the quality and effectiveness of the tests over the raw percentage. <sup>57</sup>

- **Measurement:** Code coverage will be measured using the pytest-cov plugin.* **Test Data Management:** Reusable test data, such as example API payloads, will be managed using **Pytest fixtures**. <sup>58</sup>

## **16. Security Best Practices**

### * **Input Sanitization/Validation:** All incoming API requests **must** be validated using Pydantic models. <sup>59</sup> FastAPI's automatic validation of request bodies will be the primary mechanism for preventing malformed data from being processed.

* **Secrets Management:** Sensitive information, such as API keys for external services or database credentials, **must not** be hardcoded in the source code. <sup>60</sup> They must be loaded exclusively from environment variables via the core/config.py module. <sup>61</sup>

* **Dependency Security:** The CI/CD pipeline will include a step to automatically scan for vulnerable dependencies using a tool like pip-audit. <sup>62</sup> High-severity vulnerabilities must be addressed before a deployment to production can proceed.

* **Authentication/Authorization Checks:*** As per the PRD, the Ollama-facing endpoints (/api/\*) will not have authentication.

* Any future protected endpoints **must** use a FastAPI dependency that validates the API key generated by our auth service (Epic 3). <sup>63</sup>- **Principle of Least Privilege:** The Docker container for the application will be configured to run as a non-root user to limit its privileges on the host system.

- **API Security (General):** We will implement FastAPI middleware to add standard security headers (like Content-Security-Policy, X-Content-Type-Options) to all responses to mitigate common web vulnerabilities. <sup>64</sup> HTTPS will be enforced by the hosting environment.**Error Handling & Information Disclosure:** As defined in the Error Handling Strategy, we will use custom exception handlers to ensure that no internal stack traces or sensitive system information are ever leaked in an error response to the client. <sup>65</sup>
