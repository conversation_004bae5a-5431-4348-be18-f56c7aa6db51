"""
Model list translation strategy for converting between Ollama and OpenAI API formats.

This module implements translation logic for model list responses,
converting from MedusaXD/OpenAI format to Ollama format.
"""

import logging
from datetime import datetime
from typing import List

from app.api.schemas.ollama_schemas import (
    OllamaModel,
    OllamaTagsResponse,
)
from app.api.schemas.medusa_schemas import (
    MedusaModel,
    MedusaModelListResponse,
)
from app.core.config import Settings

logger = logging.getLogger(__name__)


class ModelListTranslationStrategy:
    """
    Translation strategy for model lists.
    
    Handles translation between MedusaXD/OpenAI and Ollama model list formats.
    """
    
    def __init__(self, settings: Settings):
        """
        Initialize the model list translation strategy.
        
        Args:
            settings: Application settings containing model mappings
        """
        self.settings = settings
    
    def translate_model_list(self, medusa_response: MedusaModelListResponse) -> OllamaTagsResponse:
        """
        Translate a MedusaXD model list response to Ollama format.
        
        Args:
            medusa_response: The MedusaXD model list response
            
        Returns:
            OllamaTagsResponse: The translated response in Ollama format
        """
        logger.debug(f"Translating model list with {len(medusa_response.data)} models")
        
        ollama_models = []
        for medusa_model in medusa_response.data:
            ollama_model = self._translate_single_model(medusa_model)
            ollama_models.append(ollama_model)
        
        ollama_response = OllamaTagsResponse(models=ollama_models)
        
        logger.debug(f"Translated model list: {len(ollama_models)} models")
        return ollama_response
    
    def _translate_single_model(self, medusa_model: MedusaModel) -> OllamaModel:
        """
        Translate a single MedusaXD model to Ollama format.

        Args:
            medusa_model: The MedusaXD model

        Returns:
            OllamaModel: The translated model in Ollama format
        """
        # Convert Unix timestamp to datetime
        modified_at = datetime.fromtimestamp(medusa_model.created)

        # Apply reverse model name mapping
        model_name = self._reverse_map_model_name(medusa_model.id)

        # Default size since backend doesn't provide this information
        # In a real implementation, this could be estimated or configured
        model_size = 0

        ollama_model = OllamaModel(
            name=model_name,
            modified_at=modified_at,
            size=model_size
        )

        logger.debug(f"Translated model: {medusa_model.id} -> {model_name}")
        return ollama_model
    
    def _reverse_map_model_name(self, backend_model: str) -> str:
        """
        Reverse map a backend model name to an Ollama model name.
        
        This looks through the model mappings to find if any Ollama name
        maps to this backend model, and returns the Ollama name if found.
        
        Args:
            backend_model: The backend model name
            
        Returns:
            str: The Ollama model name (or original if no mapping found)
        """
        # Look for reverse mapping
        for ollama_name, mapped_name in self.settings.model_mappings.items():
            if mapped_name == backend_model:
                logger.debug(f"Reverse model mapping: {backend_model} -> {ollama_name}")
                return ollama_name
        
        # No mapping found, return original
        return backend_model
