"""
Health check endpoint router.

This module provides the health check endpoint for monitoring
and verifying the service status.
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from app.core.config import Settings, get_settings

logger = logging.getLogger(__name__)

router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str
    timestamp: datetime
    version: str
    service: str
    backend_url: str
    debug_mode: bool


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Health Check",
    description="Returns the current health status of the API bridge service"
)
async def health_check(settings: Settings = Depends(get_settings)) -> HealthResponse:
    """
    Health check endpoint.
    
    Returns the current status of the service including configuration
    information and timestamp for monitoring purposes.
    
    Args:
        settings: Application settings dependency
        
    Returns:
        HealthResponse: Current service health status
    """
    logger.debug("Health check requested")
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version="0.1.0",
        service="ollama-bridge",
        backend_url=settings.medusa_backend_url,
        debug_mode=settings.debug
    )
