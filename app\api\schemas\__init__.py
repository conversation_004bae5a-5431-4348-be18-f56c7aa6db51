"""API schemas package."""

from .ollama_schemas import (
    OllamaMessage,
    OllamaChatRequest,
    OllamaResponseMessage,
    OllamaChatResponse,
    OllamaStreamResponse,
    OllamaErrorResponse,
    OllamaModel,
    OllamaTagsResponse,
)

from .medusa_schemas import (
    MedusaMessage,
    MedusaChatRequest,
    MedusaUsage,
    MedusaChoice,
    MedusaChatResponse,
    MedusaStreamChoice,
    MedusaStreamResponse,
    MedusaErrorResponse,
    MedusaErrorDetail,
    MedusaModel,
    MedusaModelListResponse,
)

__all__ = [
    # Ollama schemas
    "OllamaMessage",
    "OllamaChatRequest",
    "OllamaResponseMessage",
    "OllamaChatResponse",
    "OllamaStreamResponse",
    "OllamaErrorResponse",
    "OllamaModel",
    "OllamaTagsResponse",
    # Medusa schemas
    "MedusaMessage",
    "MedusaChatRequest",
    "MedusaUsage",
    "MedusaChoice",
    "MedusaChatResponse",
    "MedusaStreamChoice",
    "MedusaStreamResponse",
    "MedusaErrorResponse",
    "MedusaErrorDetail",
    "MedusaModel",
    "MedusaModelListResponse",
]
