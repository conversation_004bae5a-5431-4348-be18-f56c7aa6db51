"""
Models router for Ollama-compatible model list endpoints.

This module provides endpoints for retrieving available models in Ollama format,
translating from the backend API format.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException

from app.api.schemas.ollama_schemas import OllamaTagsResponse
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)
from app.services.translation_service import TranslationService
from app.core.config import Settings, get_settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.get(
    "/api/tags",
    response_model=OllamaTagsResponse,
    summary="List Available Models",
    description="Get a list of available models in Ollama format",
    responses={
        200: {"description": "Successful model list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_models(
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaTagsResponse:
    """
    List available models in Ollama format.
    
    This endpoint retrieves the model list from the backend API and translates
    it to the Ollama /api/tags format for compatibility with Ollama clients.
    
    Args:
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaTagsResponse: The model list in Ollama format
        
    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.debug("Model list request received")
    
    try:
        # Get model list from backend
        logger.debug("Requesting model list from backend")
        medusa_response = await backend_client.get_models()
        
        # Translate to Ollama format
        logger.debug("Translating model list to Ollama format")
        ollama_response = translation_service.translate_model_list(medusa_response)
        
        logger.info(f"Model list retrieved successfully: {len(ollama_response.models)} models")
        return ollama_response
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during model list request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving model list"
        )
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Unable to connect to backend service"
        )
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail=f"Backend service error: {e}"
        )
        
    except BackendClientError as e:
        logger.error(f"Backend client error during model list request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        )
        
    except Exception as e:
        logger.error(f"Unexpected error during model list request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )
